<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>QR Code Image Generator</title>
    <script src="https://cdn.tailwindcss.com"></script>
  <script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@18.2.0",
    "react-dom/client": "https://esm.sh/react-dom@18.2.0/client",
    "lucide-react": "https://esm.sh/lucide-react@0.378.0",
    "qrcode": "https://esm.sh/qrcode@1.5.3",
    "jszip": "https://esm.sh/jszip@3.10.1",
    "file-saver": "https://esm.sh/file-saver@2.0.5"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
  <body class="bg-slate-50 dark:bg-slate-900">
    <div id="root"></div>
    <script type="module">
      import React, { useState, useCallback, useEffect } from 'react';
      import ReactDOM from 'react-dom/client';
      import QRCode from 'qrcode';
      import JSZip from 'jszip';
      import saveAs from 'file-saver';
      import { UploadCloud, FileText, Type, Image as ImageIcon, Download, AlertTriangle, Archive } from 'lucide-react';

      // --- Services ---
      const generateCompositeImage = async ({ url, logoImage, customText }) => {
        try {
          // 1. Generate QR Code Canvas
          const qrCanvas = document.createElement('canvas');
          await QRCode.toCanvas(qrCanvas, url, {
            errorCorrectionLevel: 'H',
            margin: 4,
            scale: 10,
            color: {
              dark: '#005225',
              light: '#FFFFFF',
            },
            width: 500,
          });
          const qrWidth = qrCanvas.width;
          const qrHeight = qrCanvas.height;

          // 2. Prepare Logo
          const logoBaseWidth = qrWidth / 1.1;
          const logoRatio = logoBaseWidth / logoImage.width;
          const logoWidth = logoBaseWidth;
          const logoHeight = logoImage.height * logoRatio;

          // 3. Prepare Text and calculate its dimensions
          const tempCtx = document.createElement('canvas').getContext('2d');
          const fontSize = 44;
          const font = `bold ${fontSize}px Arial, sans-serif`;
          tempCtx.font = font;
          
          const textLines = customText.split('\n');
          const lineHeight = fontSize * 1.4;
          const totalTextHeight = textLines.length * lineHeight;

          // 4. Calculate final image dimensions
          const padding = 30;
          const totalHeight = qrHeight + padding + totalTextHeight + padding + logoHeight + padding;
          
          const finalCanvas = document.createElement('canvas');
          finalCanvas.width = qrWidth;
          finalCanvas.height = totalHeight;
          const ctx = finalCanvas.getContext('2d');

          // 5. Compose the final image
          ctx.fillStyle = 'white';
          ctx.fillRect(0, 0, finalCanvas.width, finalCanvas.height);
          ctx.drawImage(qrCanvas, 0, 0);

          ctx.fillStyle = '#005225';
          ctx.font = font;
          ctx.textAlign = 'center';
          ctx.textBaseline = 'top';

          let currentY = qrHeight + padding;
          for (const line of textLines) {
            ctx.fillText(line, qrWidth / 2, currentY);
            currentY += lineHeight;
          }

          const logoX = (qrWidth - logoWidth) / 2;
          const logoY = currentY + padding / 2;
          ctx.drawImage(logoImage, logoX, logoY, logoWidth, logoHeight);

          // 6. Return Data URL
          return finalCanvas.toDataURL('image/png');
        } catch (error) {
          console.error('Error generating composite image:', error);
          throw new Error('Failed to generate QR code image.');
        }
      };

      // --- Components ---
      const Loader = ({ size = 'md' }) => {
        const sizeClasses = {
          sm: 'h-5 w-5',
          md: 'h-6 w-6',
          lg: 'h-10 w-10',
        };
        return React.createElement(
          'svg',
          {
            className: `animate-spin ${sizeClasses[size]}`,
            xmlns: "http://www.w3.org/2000/svg",
            fill: "none",
            viewBox: "0 0 24 24"
          },
          React.createElement('circle', {
            className: "opacity-25",
            cx: "12",
            cy: "12",
            r: "10",
            stroke: "currentColor",
            strokeWidth: "4"
          }),
          React.createElement('path', {
            className: "opacity-75",
            fill: "currentColor",
            d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          })
        );
      };

      const QrCodeCard = ({ name, dataUrl }) => {
        const fileName = `${name.replace(/ /g, '_')}_qr.png`;
        return React.createElement('div', { className: "bg-white dark:bg-slate-800 rounded-lg shadow-md p-4 border border-slate-200 dark:border-slate-700 flex flex-col items-center gap-4 transition-transform transform hover:scale-105 hover:shadow-xl" },
          React.createElement('div', { className: "w-full aspect-square bg-slate-100 dark:bg-slate-700 rounded-md overflow-hidden flex items-center justify-center p-2" },
            React.createElement('img', { src: dataUrl, alt: `QR Code for ${name}`, className: "max-w-full max-h-full object-contain" })
          ),
          React.createElement('div', { className: "text-center w-full" },
            React.createElement('p', { className: "font-semibold text-slate-800 dark:text-slate-100 truncate", title: name }, name),
            React.createElement('a', {
              href: dataUrl,
              download: fileName,
              className: "mt-3 inline-flex items-center justify-center gap-2 w-full text-white bg-emerald-600 hover:bg-emerald-700 focus:ring-4 focus:ring-emerald-300 dark:focus:ring-emerald-800 font-medium rounded-lg text-sm px-4 py-2 transition-colors"
            },
            React.createElement(Download, { size: 16 }),
            'Download'
            )
          )
        );
      };

      // --- App Component ---
      const App = () => {
        const [urlsText, setUrlsText] = useState('');
        const [logoFile, setLogoFile] = useState(null);
        const [logoPreview, setLogoPreview] = useState(null);
        const [customText, setCustomText] = useState('file-name\nDigipäevikud');
        const [generatedImages, setGeneratedImages] = useState([]);
        const [isLoading, setIsLoading] = useState(false);
        const [isZipping, setIsZipping] = useState(false);
        const [error, setError] = useState(null);

        useEffect(() => {
          const loadDefaultLogo = async () => {
            try {
              const response = await fetch('PKVH_logo.jpg');
              if (!response.ok) {
                throw new Error('Default logo not found');
              }
              const blob = await response.blob();
              const defaultLogoFile = new File([blob], 'PKVH_logo.jpg', { type: 'image/jpeg' });
              setLogoFile(defaultLogoFile);
              setLogoPreview(URL.createObjectURL(blob));
            } catch (err) {
              console.error('Failed to load default logo:', err);
              // Silently fail, user can still upload a logo manually.
            }
          };
          loadDefaultLogo();
        }, []);

        const handleLogoChange = (e) => {
          const file = e.target.files?.[0];
          if (file) {
            if (file.type.startsWith('image/')) {
              setLogoFile(file);
              setLogoPreview(URL.createObjectURL(file));
              setError(null);
            } else {
              setError('Please upload a valid image file (e.g., PNG, JPEG).');
              setLogoFile(null);
              setLogoPreview(null);
            }
          }
        };

        const parseLine = (line, index) => {
          const delimiters = [' - ', ',', ';'];
          for (const delimiter of delimiters) {
            const pos = line.indexOf(delimiter);
            if (pos !== -1) {
              const url = line.substring(0, pos).trim();
              const clientName = line.substring(pos + delimiter.length).trim();
              if (url && clientName) {
                return { url, clientName };
              }
            }
          }
          const trimmedLine = line.trim();
          return { url: trimmedLine, clientName: `client${index + 1}` };
        };

        const handleGenerate = useCallback(async () => {
          if (!urlsText.trim()) {
            setError('URL list cannot be empty.');
            return;
          }
          if (!logoFile) {
            setError('Please upload a logo image.');
            return;
          }

          setIsLoading(true);
          setError(null);
          setGeneratedImages([]);

          const logoImage = new Image();
          logoImage.src = URL.createObjectURL(logoFile);

          logoImage.onload = async () => {
            const lines = urlsText.split('\n').filter(line => line.trim() !== '');
            const urlData = lines.map(parseLine);
            
            const results = [];
            try {
              for (const data of urlData) {
                const finalCustomText = customText.replace('file-name', data.clientName);
                const params = {
                  url: data.url,
                  logoImage,
                  customText: finalCustomText,
                };
                const dataUrl = await generateCompositeImage(params);
                results.push({ name: data.clientName, dataUrl });
              }
              setGeneratedImages(results);
            } catch (e) {
              console.error('Generation failed:', e);
              setError(`An error occurred during QR code generation: ${e instanceof Error ? e.message : String(e)}`);
            } finally {
              setIsLoading(false);
            }
          };
          
          logoImage.onerror = () => {
              setError('Failed to load the logo image. Please try another file.');
              setIsLoading(false);
          }
        }, [urlsText, logoFile, customText]);
        
        const handleDownloadAll = useCallback(async () => {
          if (generatedImages.length === 0 || isZipping) return;

          setIsZipping(true);
          setError(null);

          try {
            const zip = new JSZip();
            for (const image of generatedImages) {
              const fileName = `${image.name.replace(/ /g, '_')}_qr.png`;
              const base64Data = image.dataUrl.split(',')[1];
              if (base64Data) {
                zip.file(fileName, base64Data, { base64: true });
              }
            }
            const zipBlob = await zip.generateAsync({ type: 'blob' });
            saveAs(zipBlob, 'qr_codes.zip');
          } catch (e) {
            console.error('Failed to create zip file:', e);
            setError('Could not create the zip file. Please try downloading images individually.');
          } finally {
            setIsZipping(false);
          }
        }, [generatedImages, isZipping]);

        return React.createElement('div', { className: "min-h-screen text-slate-800 dark:text-slate-200" },
          React.createElement('div', { className: "container mx-auto p-4 md:p-8" },
            React.createElement('header', { className: "text-center mb-8" },
              React.createElement('h1', { className: "text-4xl md:text-5xl font-bold text-emerald-600 dark:text-emerald-500" }, 'Pindi QR Code Generator'),
              React.createElement('p', { className: "text-slate-600 dark:text-slate-400 mt-2 max-w-2xl mx-auto" }, 'Create beautiful QR codes with your logo and custom text. Paste your URLs, upload a logo, and get your images instantly.')
            ),
            React.createElement('main', { className: "grid grid-cols-1 lg:grid-cols-2 gap-8" },
              React.createElement('div', { className: "bg-white dark:bg-slate-800 p-6 rounded-2xl shadow-lg border border-slate-200 dark:border-slate-700" },
                React.createElement('h2', { className: "text-2xl font-semibold mb-6 flex items-center gap-3" }, React.createElement(Download, { className: "text-emerald-500" }), '1. Provide Your Data'),
                error && React.createElement('div', { className: "bg-red-100 dark:bg-red-900/30 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 px-4 py-3 rounded-lg relative mb-6 flex items-start gap-3", role: "alert" },
                  React.createElement(AlertTriangle, { className: "h-5 w-5 mt-0.5" }),
                  React.createElement('span', { className: "block sm:inline" }, error)
                ),
                React.createElement('div', { className: "space-y-6" },
                  React.createElement('div', null,
                    React.createElement('label', { htmlFor: "urls", className: "block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 flex items-center gap-2" }, React.createElement(FileText, { size: 16 }), ' URLs & Client Names'),
                    React.createElement('textarea', { id: "urls", rows: 8, className: "w-full p-3 bg-slate-50 dark:bg-slate-700/50 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition duration-200", placeholder: "e.g.\nhttps://example.com/page1, Client A\nhttps://example.com/page2 - Client B\nhttps://example.com/page3; Client C", value: urlsText, onChange: (e) => setUrlsText(e.target.value) }),
                    React.createElement('p', { className: "text-xs text-slate-500 dark:text-slate-400 mt-1" }, "One entry per line. Separate URL and name with a comma, semicolon, or ' - '.")
                  ),
                  React.createElement('div', null,
                    React.createElement('label', { htmlFor: "customText", className: "block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 flex items-center gap-2" }, React.createElement(Type, { size: 16 }), ' Custom Text'),
                    React.createElement('textarea', { id: "customText", rows: 3, className: "w-full p-3 bg-slate-50 dark:bg-slate-700/50 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition duration-200", value: customText, onChange: (e) => setCustomText(e.target.value) }),
                    React.createElement('p', { className: "text-m text-slate-500 dark:text-slate-400 mt-1" }, "To automatically generate aadress on QR first line must be 'file-name' text.This text will appear below the QR code. Use a new line for line breaks.")
                  ),
                  React.createElement('div', null,
                    React.createElement('label', { className: "block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 flex items-center gap-2" }, React.createElement(ImageIcon, { size: 16 }), ' Pindi Logo'),
                    React.createElement('div', { className: "mt-1 flex items-center gap-4" },
                      React.createElement('div', { className: "flex-grow" },
                        React.createElement('label', { htmlFor: "logo-upload", className: "relative cursor-pointer bg-white dark:bg-slate-700 rounded-md font-medium text-emerald-600 dark:text-emerald-400 hover:text-emerald-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-emerald-500 border dark:border-slate-600 p-2 text-center" },
                          React.createElement('div', { className: "flex items-center justify-center gap-2" },
                            React.createElement(UploadCloud, { size: 18 }),
                            React.createElement('span', null, logoFile ? 'Change Logo' : 'Upload Logo')
                          ),
                          React.createElement('input', { id: "logo-upload", name: "logo-upload", type: "file", className: "sr-only", onChange: handleLogoChange, accept: "image/*" })
                        )
                      ),
                      logoPreview && React.createElement('div', { className: "w-24 h-24 flex-shrink-0 bg-slate-100 dark:bg-slate-700 rounded-lg flex items-center justify-center p-1 border border-slate-200 dark:border-slate-600" },
                        React.createElement('img', { src: logoPreview, alt: "Logo Preview", className: "max-w-full max-h-full object-contain" })
                      )
                    )
                  )
                ),
                React.createElement('div', { className: "mt-8" },
                  React.createElement('button', { onClick: handleGenerate, disabled: isLoading || !urlsText || !logoFile, className: "w-full flex items-center justify-center gap-3 text-white bg-emerald-600 hover:bg-emerald-700 focus:ring-4 focus:ring-emerald-300 dark:focus:ring-emerald-800 font-medium rounded-lg text-lg px-5 py-3.5 transition-all duration-300 disabled:bg-slate-400 dark:disabled:bg-slate-600 disabled:cursor-not-allowed" },
                    isLoading ? React.createElement(Loader) : 'Generate QR Codes'
                  )
                )
              ),
              React.createElement('div', { className: "bg-white dark:bg-slate-800/50 p-6 rounded-2xl shadow-inner border border-slate-200 dark:border-slate-700 min-h-[30rem] flex flex-col" },
                React.createElement('div', { className: "flex justify-between items-center mb-6 flex-shrink-0" },
                  React.createElement('h2', { className: "text-2xl font-semibold flex items-center gap-3" }, React.createElement(Download, { className: "text-emerald-500" }), '2. Download Your Images'),
                  generatedImages.length > 0 && !isLoading && React.createElement('button', { onClick: handleDownloadAll, disabled: isZipping, className: "flex items-center justify-center gap-2 text-white bg-indigo-600 hover:bg-indigo-700 focus:ring-4 focus:ring-indigo-300 dark:focus:ring-indigo-800 font-medium rounded-lg text-sm px-4 py-2 transition-all duration-300 disabled:bg-slate-400 dark:disabled:bg-slate-600 disabled:cursor-not-allowed" },
                    isZipping ? React.createElement(Loader, { size: "sm" }) : React.createElement(Archive, { size: 16 }),
                    isZipping ? 'Zipping...' : 'Download All'
                  )
                ),
                React.createElement('div', { className: "flex-grow overflow-hidden" },
                  isLoading && React.createElement('div', { className: "flex flex-col items-center justify-center h-full text-slate-500 dark:text-slate-400" },
                    React.createElement(Loader, { size: "lg" }),
                    React.createElement('p', { className: "mt-4" }, "Generating images, please wait...")
                  ),
                  !isLoading && generatedImages.length === 0 && React.createElement('div', { className: "flex flex-col items-center justify-center h-full text-center text-slate-500 dark:text-slate-400" },
                    React.createElement(ImageIcon, { size: 48, className: "mb-4" }),
                    React.createElement('h3', { className: "text-lg font-semibold" }, "Your QR codes will appear here."),
                    React.createElement('p', null, "Fill out the form to get started.")
                  ),
                  !isLoading && generatedImages.length > 0 && React.createElement('div', { className: "grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-2 gap-6 h-full overflow-y-auto pr-2" },
                    generatedImages.map((image) => React.createElement(QrCodeCard, { key: image.name, name: image.name, dataUrl: image.dataUrl }))
                  )
                )
              )
            )
          )
        );
      };

      // --- Mount Application ---
      const rootElement = document.getElementById('root');
      if (!rootElement) {
        throw new Error("Could not find root element to mount to");
      }
      const root = ReactDOM.createRoot(rootElement);
      root.render(
        React.createElement(React.StrictMode, null, React.createElement(App))
      );
    </script>
  <script type="module" src="/index.tsx"></script>
</body>
</html>